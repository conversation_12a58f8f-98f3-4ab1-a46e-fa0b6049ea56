import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for course class updates
const courseClassUpdateSchema = z.object({
  syllabusUrl: z.string().url().optional().or(z.literal('')),
  description: z.string().optional(),
  maxEnrollment: z.number().min(1).optional()
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const facultyProfile = await prisma.facultyProfile.findUnique({
      where: { userId: session.user.id },
      include: {
        classes: {
          include: {
            course: {
              include: {
                department: {
                  select: {
                    name: true,
                    slug: true
                  }
                }
              }
            }
          },
          orderBy: [
            { year: 'desc' },
            { semester: 'desc' }
          ]
        }
      }
    })

    if (!facultyProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    return NextResponse.json({ courses: facultyProfile.classes })
  } catch (error) {
    console.error('Error fetching faculty courses:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { courseClassId, ...updateData } = body
    const validatedData = courseClassUpdateSchema.parse(updateData)

    // Verify the course class belongs to the current faculty member
    const courseClass = await prisma.courseClass.findFirst({
      where: {
        id: courseClassId,
        faculty: {
          userId: session.user.id
        }
      }
    })

    if (!courseClass) {
      return NextResponse.json({ error: 'Course class not found' }, { status: 404 })
    }

    // Update course class
    const updatedCourseClass = await prisma.courseClass.update({
      where: { id: courseClassId },
      data: {
        ...validatedData,
        syllabusUrl: validatedData.syllabusUrl || null,
        description: validatedData.description || null
      },
      include: {
        course: {
          include: {
            department: {
              select: {
                name: true,
                slug: true
              }
            }
          }
        }
      }
    })

    return NextResponse.json(updatedCourseClass)
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation failed', 
        details: error.errors 
      }, { status: 400 })
    }

    console.error('Error updating course class:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
