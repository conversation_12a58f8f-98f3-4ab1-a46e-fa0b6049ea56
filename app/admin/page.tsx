import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import {
  Users,
  FileText,
  Building2,
  BookOpen,
  TrendingUp,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  Activity,
  Clock,
  CheckCircle,
  AlertTriangle,
  GraduationCap,
  Globe,
  Zap,
  Shield,
  Eye,
  BarChart3,
  RefreshCw,
  Plus,
  ExternalLink
} from "lucide-react"
import { prisma } from "@/lib/prisma"
import { UserRole, UserStatus } from "@prisma/client"
import Link from "next/link"

async function getDashboardStats() {
  const now = new Date()
  const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate())
  const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)

  const [
    totalUsers,
    newUsersThisMonth,
    activeFaculty,
    totalPosts,
    publishedPosts,
    postsThisMonth,
    totalDepartments,
    totalPrograms,
    totalCourses,
    pendingUsers,
    totalViews,
    systemHealth
  ] = await Promise.all([
    prisma.user.count(),
    prisma.user.count({
      where: {
        createdAt: {
          gte: lastMonth
        }
      }
    }),
    prisma.user.count({
      where: {
        role: UserRole.FACULTY,
        status: UserStatus.ACTIVE
      }
    }),
    prisma.post.count(),
    prisma.post.count({
      where: {
        status: 'PUBLISHED'
      }
    }),
    prisma.post.count({
      where: {
        createdAt: {
          gte: lastMonth
        }
      }
    }),
    prisma.department.count(),
    prisma.program.count(),
    prisma.course.count(),
    prisma.user.count({
      where: {
        status: UserStatus.PENDING
      }
    }),
    prisma.post.aggregate({
      _sum: {
        viewCount: true
      }
    }),
    // Mock system health data
    Promise.resolve({
      uptime: 99.9,
      responseTime: 120,
      errorRate: 0.1
    })
  ])

  // Calculate growth percentages
  const userGrowth = totalUsers > 0 ? ((newUsersThisMonth / totalUsers) * 100) : 0
  const postGrowth = totalPosts > 0 ? ((postsThisMonth / totalPosts) * 100) : 0

  return {
    totalUsers,
    newUsersThisMonth,
    userGrowth,
    activeFaculty,
    totalPosts,
    publishedPosts,
    postsThisMonth,
    postGrowth,
    totalDepartments,
    totalPrograms,
    totalCourses,
    pendingUsers,
    totalViews: totalViews._sum.viewCount || 0,
    systemHealth
  }
}

async function getRecentActivity() {
  const recentPosts = await prisma.post.findMany({
    take: 5,
    orderBy: {
      createdAt: 'desc'
    },
    include: {
      author: {
        select: {
          name: true,
          email: true
        }
      },
      category: {
        select: {
          name: true
        }
      }
    }
  })

  const recentUsers = await prisma.user.findMany({
    take: 5,
    orderBy: {
      createdAt: 'desc'
    },
    select: {
      id: true,
      name: true,
      email: true,
      role: true,
      status: true,
      createdAt: true
    }
  })

  return {
    recentPosts,
    recentUsers
  }
}

export default async function AdminDashboard() {
  const stats = await getDashboardStats()
  const activity = await getRecentActivity()

  const statCards = [
    {
      title: "Total Users",
      value: stats.totalUsers.toLocaleString(),
      change: `+${stats.newUsersThisMonth}`,
      changePercent: stats.userGrowth,
      description: "All registered users",
      icon: Users,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      trend: stats.userGrowth > 0 ? 'up' : 'down'
    },
    {
      title: "Active Faculty",
      value: stats.activeFaculty.toLocaleString(),
      change: null,
      changePercent: null,
      description: "Faculty members",
      icon: GraduationCap,
      color: "text-green-600",
      bgColor: "bg-green-50",
      trend: null
    },
    {
      title: "Published Posts",
      value: stats.publishedPosts.toLocaleString(),
      change: `+${stats.postsThisMonth}`,
      changePercent: stats.postGrowth,
      description: `${stats.totalPosts} total posts`,
      icon: FileText,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      trend: stats.postGrowth > 0 ? 'up' : 'down'
    },
    {
      title: "Total Views",
      value: stats.totalViews.toLocaleString(),
      change: null,
      changePercent: null,
      description: "Content views",
      icon: Eye,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      trend: null
    },
    {
      title: "Departments",
      value: stats.totalDepartments.toLocaleString(),
      change: null,
      changePercent: null,
      description: "Academic departments",
      icon: Building2,
      color: "text-indigo-600",
      bgColor: "bg-indigo-50",
      trend: null
    },
    {
      title: "System Health",
      value: `${stats.systemHealth.uptime}%`,
      change: null,
      changePercent: null,
      description: "Uptime",
      icon: Activity,
      color: "text-emerald-600",
      bgColor: "bg-emerald-50",
      trend: null
    }
  ]

  const quickActions = [
    {
      title: "Add Faculty",
      description: "Create new faculty profile",
      href: "/admin/faculty/new",
      icon: GraduationCap,
      color: "bg-blue-500"
    },
    {
      title: "New Post",
      description: "Create content article",
      href: "/admin/posts/new",
      icon: FileText,
      color: "bg-green-500"
    },
    {
      title: "Add Department",
      description: "Create new department",
      href: "/admin/departments/new",
      icon: Building2,
      color: "bg-purple-500"
    },
    {
      title: "View Analytics",
      description: "Performance insights",
      href: "/admin/analytics",
      icon: BarChart3,
      color: "bg-orange-500"
    }
  ]

  const systemAlerts = [
    ...(stats.pendingUsers > 0 ? [{
      type: 'warning' as const,
      title: 'Pending User Approvals',
      message: `${stats.pendingUsers} users waiting for approval`,
      action: 'Review Users',
      href: '/admin/users?status=pending'
    }] : []),
    ...(stats.systemHealth.errorRate > 1 ? [{
      type: 'error' as const,
      title: 'High Error Rate',
      message: `Error rate: ${stats.systemHealth.errorRate}%`,
      action: 'View Logs',
      href: '/admin/logs'
    }] : []),
    ...(stats.systemHealth.responseTime > 500 ? [{
      type: 'warning' as const,
      title: 'Slow Response Time',
      message: `Average: ${stats.systemHealth.responseTime}ms`,
      action: 'Check Performance',
      href: '/admin/performance'
    }] : [])
  ]

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
            Dashboard
          </h1>
          <p className="text-gray-600 mt-1">Welcome back! Here's what's happening with your college.</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button size="sm">
            <Plus className="w-4 h-4 mr-2" />
            Quick Add
          </Button>
        </div>
      </div>

      {/* System Alerts */}
      {systemAlerts.length > 0 && (
        <div className="space-y-3">
          {systemAlerts.map((alert, index) => (
            <Card key={index} className={`border-l-4 ${
              alert.type === 'error' ? 'border-l-red-500 bg-red-50' :
              alert.type === 'warning' ? 'border-l-yellow-500 bg-yellow-50' :
              'border-l-blue-500 bg-blue-50'
            }`}>
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {alert.type === 'error' ? (
                      <AlertTriangle className="w-5 h-5 text-red-500" />
                    ) : alert.type === 'warning' ? (
                      <AlertTriangle className="w-5 h-5 text-yellow-500" />
                    ) : (
                      <CheckCircle className="w-5 h-5 text-blue-500" />
                    )}
                    <div>
                      <h4 className="font-medium text-gray-900">{alert.title}</h4>
                      <p className="text-sm text-gray-600">{alert.message}</p>
                    </div>
                  </div>
                  <Link href={alert.href}>
                    <Button variant="outline" size="sm">
                      {alert.action}
                      <ExternalLink className="w-3 h-3 ml-2" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Enhanced Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {statCards.map((stat, index) => (
          <Card key={index} className="relative overflow-hidden hover:shadow-lg transition-shadow duration-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                {stat.title}
              </CardTitle>
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`h-5 w-5 ${stat.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-3xl font-bold text-gray-900">{stat.value}</div>
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-600">{stat.description}</p>
                  {stat.change && (
                    <div className={`flex items-center text-sm ${
                      stat.trend === 'up' ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {stat.trend === 'up' ? (
                        <ArrowUpRight className="w-4 h-4 mr-1" />
                      ) : (
                        <ArrowDownRight className="w-4 h-4 mr-1" />
                      )}
                      <span className="font-medium">{stat.change}</span>
                      {stat.changePercent && (
                        <span className="ml-1">({stat.changePercent.toFixed(1)}%)</span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Activity - Takes 2 columns */}
        <div className="lg:col-span-2 space-y-6">
          {/* Recent Posts */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="flex items-center">
                  <FileText className="w-5 h-5 mr-2 text-purple-600" />
                  Recent Posts
                </CardTitle>
                <CardDescription>Latest content updates</CardDescription>
              </div>
              <Link href="/admin/posts">
                <Button variant="outline" size="sm">
                  View All
                  <ExternalLink className="w-3 h-3 ml-2" />
                </Button>
              </Link>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activity.recentPosts.length > 0 ? (
                  activity.recentPosts.map((post) => (
                    <div key={post.id} className="flex items-start space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center">
                        <FileText className="w-5 h-5 text-purple-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {post.title}
                        </p>
                        <p className="text-sm text-gray-500 mt-1">
                          by {post.author.name || post.author.email}
                        </p>
                        <div className="flex items-center mt-2 space-x-2">
                          <Badge variant="secondary" className="text-xs">
                            {post.category.name}
                          </Badge>
                          <span className="text-xs text-gray-400">
                            {new Date(post.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <FileText className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No posts yet</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Recent Users */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle className="flex items-center">
                  <Users className="w-5 h-5 mr-2 text-blue-600" />
                  Recent Users
                </CardTitle>
                <CardDescription>Newly registered users</CardDescription>
              </div>
              <Link href="/admin/users">
                <Button variant="outline" size="sm">
                  View All
                  <ExternalLink className="w-3 h-3 ml-2" />
                </Button>
              </Link>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activity.recentUsers.length > 0 ? (
                  activity.recentUsers.map((user) => (
                    <div key={user.id} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                        <Users className="w-5 h-5 text-blue-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {user.name || user.email}
                        </p>
                        <div className="flex items-center mt-1 space-x-2">
                          <Badge
                            variant={user.status === 'ACTIVE' ? 'default' : 'secondary'}
                            className="text-xs"
                          >
                            {user.role.toLowerCase().replace('_', ' ')}
                          </Badge>
                          <span className="text-xs text-gray-400">
                            {new Date(user.createdAt).toLocaleDateString()}
                          </span>
                        </div>
                      </div>
                      <Badge
                        variant={user.status === 'ACTIVE' ? 'default' : 'outline'}
                        className="text-xs"
                      >
                        {user.status.toLowerCase()}
                      </Badge>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>No users yet</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar - Takes 1 column */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="w-5 h-5 mr-2 text-yellow-500" />
                Quick Actions
              </CardTitle>
              <CardDescription>Common tasks</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {quickActions.map((action, index) => (
                  <Link key={index} href={action.href}>
                    <div className="flex items-center p-3 rounded-lg border hover:bg-gray-50 transition-colors cursor-pointer">
                      <div className={`w-10 h-10 rounded-lg ${action.color} flex items-center justify-center mr-3`}>
                        <action.icon className="w-5 h-5 text-white" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-sm">{action.title}</h4>
                        <p className="text-xs text-gray-500">{action.description}</p>
                      </div>
                      <ExternalLink className="w-4 h-4 text-gray-400" />
                    </div>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* System Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="w-5 h-5 mr-2 text-green-500" />
                System Status
              </CardTitle>
              <CardDescription>Current system health</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Uptime</span>
                    <span className="text-sm text-green-600">{stats.systemHealth.uptime}%</span>
                  </div>
                  <Progress value={stats.systemHealth.uptime} className="h-2" />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Response Time</span>
                    <span className="text-sm text-gray-600">{stats.systemHealth.responseTime}ms</span>
                  </div>
                  <Progress
                    value={Math.max(0, 100 - (stats.systemHealth.responseTime / 10))}
                    className="h-2"
                  />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Error Rate</span>
                    <span className="text-sm text-gray-600">{stats.systemHealth.errorRate}%</span>
                  </div>
                  <Progress
                    value={Math.max(0, 100 - stats.systemHealth.errorRate * 10)}
                    className="h-2"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="w-5 h-5 mr-2 text-indigo-500" />
                Quick Stats
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Total Courses</span>
                  <span className="font-medium">{stats.totalCourses}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Programs</span>
                  <span className="font-medium">{stats.totalPrograms}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Departments</span>
                  <span className="font-medium">{stats.totalDepartments}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Content Views</span>
                  <span className="font-medium">{stats.totalViews.toLocaleString()}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
