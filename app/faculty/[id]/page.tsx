import Link from 'next/link'
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  ArrowLeft,
  Mail,
  Globe,
  BookOpen,
  Award,
  FileText,
  Briefcase,
  Clock,
  FileDown,
  UserPlus,
  Menu,
  ChevronRight,
  BarChart,
  User as UserIcon,
  MapPin,
  Link2,
  Bookmark,
  Calendar,
  Users as UsersIcon,
  ExternalLink,
  Quote,
  Hash,
  GraduationCap,
  ArrowUpRight
} from "lucide-react"
import { LazyImage } from "@/components/ui/lazy-image"

// Import Prisma types
import { 
  User, 
  UserProfile, 
  FacultyProfile, 
  Department, 
  FacultyEducation, 
  FacultyPublication, 
  FacultyResearchArea, 
  FacultyTimeline, 
  FacultyIndustryExperience, 
  FacultySkill, 
  ResearchProject, 
  CourseClass,
  Prisma
} from "@prisma/client"

// Import shared components
import { AcademicTimeline } from "@/components/ui/academic-timeline"
import { CVDownload } from "@/components/ui/cv-download"
import { ScholarlyPublications } from "@/components/ui/scholarly-publications"
import { UpcomingClasses } from "@/components/ui/upcoming-classes"
import { OfficeHoursScheduler } from "@/components/ui/office-hours-scheduler"
import { ResearchOpportunities } from "@/components/ui/research-opportunities"
import { ResearchAreas } from "@/components/ui/research-areas"
import { EducationTimeline } from "@/components/ui/education-timeline"

// Import new components
import { Breadcrumb, BreadcrumbItem } from "@/components/ui/breadcrumb"
import { PrintButton } from "@/components/ui/print-button"
import { PrintStyles } from "@/components/ui/print-styles"
import { prisma } from "@/lib/prisma"

// Define a comprehensive type for the faculty data
type FacultyPageData = User & {
  profile: UserProfile | null;
  facultyProfile: FacultyProfile & {
    department: Department;
    education: FacultyEducation[];
    publications: FacultyPublication[];
    researchAreas: FacultyResearchArea[];
    timeline: FacultyTimeline[];
    industryExperience: FacultyIndustryExperience[];
    skills: FacultySkill[];
    researchProjects: ResearchProject[];
    classes: CourseClass[];
  } & {
    officeLocation?: string | null;
    websiteUrl?: string | null;
    scholarId?: string | null;
    bio?: string | null;
    courses?: string[]; 
    upcomingClasses?: CourseClass[]; 
    officeHours?: string | null;
    scheduledOfficeHours?: any[]; 
    citationCount?: number | null;
    hIndex?: number | null;
  };
};

interface FacultyPageProps {
  params: {
    id: string
  }
}

async function getFacultyById(id: string): Promise<FacultyPageData | null> {
  const user = await prisma.user.findUnique({
    where: {
      id,
      role: 'FACULTY',
      status: 'ACTIVE',
    },
    include: {
      profile: true,
      facultyProfile: {
        include: {
          department: true,
          education: {
            orderBy: {
              year: 'desc'
            }
          },
          publications: {
            orderBy: {
              year: 'desc'
            }
          },
          researchAreas: true,
          timeline: {
            orderBy: {
              year: 'desc'
            }
          },
          industryExperience: {
            orderBy: {
              startDate: 'desc'
            }
          },
          skills: {
            orderBy: [
              {
                category: 'asc'
              },
              {
                skillName: 'asc'
              }
            ]
          },
          researchProjects: true,
          classes: {
            include: {
              course: {
                include: {
                  department: true
                }
              }
            },
            where: {
              status: 'current'
            }
          },
          officeHours: {
            where: { isActive: true },
            orderBy: [
              { dayOfWeek: 'asc' },
              { startTime: 'asc' }
            ]
          }
        }
      }
    }
  })

  if (!user || !user.facultyProfile) {
    return null
  }

  return user as FacultyPageData;
}

export default async function FacultyProfilePage({ params }: FacultyPageProps) {
  const { id } = await params;
  const faculty: FacultyPageData | null = await getFacultyById(id);

  if (!faculty || !faculty.facultyProfile) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md p-8">
            <h1 className="text-xl font-medium mb-2 text-gray-900">Faculty Not Found</h1>
            <p className="mb-6 text-muted-foreground text-sm">
              We couldn't find the faculty member you're looking for. They may have moved to a different department or the URL might be incorrect.
            </p>
            <Link href="/faculty">
              <Button variant="outline" size="sm" className="h-9 px-4 border-crimson/20 hover:bg-crimson/5">
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Faculty Directory
              </Button>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Build breadcrumb items
  const breadcrumbItems: BreadcrumbItem[] = [
    { label: 'Faculty', href: '/faculty' },
    { label: faculty.facultyProfile.department.name, href: `/faculty?department=${encodeURIComponent(faculty.facultyProfile.department.name)}` },
    { label: faculty.name || '', href: `/faculty/${faculty.id}`, current: true }
  ];

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <PrintStyles selector="#print-content" />
        <main id="main-content" className="flex-1 bg-light/20" tabIndex={-1}>
          {/* Breadcrumb Navigation */}
          <div className="bg-white border-b border-gray-100">
            <div className="container mx-auto px-4 max-w-6xl py-3">
              <Breadcrumb items={breadcrumbItems} />
            </div>
          </div>

          {/* Faculty Profile Hero Section */}
          <section className="relative overflow-hidden">
            {/* Decorative background */}
            <div className="absolute inset-0 bg-gradient-to-br from-crimson/5 via-transparent to-gold/5"></div>
            <div className="absolute top-0 left-0 w-full h-72 bg-gradient-to-b from-white via-white to-transparent"></div>
            <div className="absolute -top-24 -left-24 w-96 h-96 rounded-full bg-crimson/5 blur-3xl"></div>
            <div className="absolute top-20 right-10 w-80 h-80 rounded-full bg-gold/5 blur-3xl"></div>
            
            <div className="container mx-auto px-4 max-w-6xl pt-10 pb-0 relative z-10">
              <div className="flex justify-between items-center mb-6">
                <Link href="/faculty" aria-label="Back to Faculty Directory">
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-gray-900 p-2 h-8">
                    <ArrowLeft className="h-4 w-4 mr-1" />
                    <span className="text-sm">Back</span>
                  </Button>
                </Link>

                <div className="flex items-center gap-2 print-hide">
                  <PrintButton
                    variant="ghost"
                    size="sm"
                    className="text-gray-600 h-8"
                  />
                </div>
              </div>

              <div id="print-content" className="flex flex-col lg:flex-row gap-12 mb-0">
                <div className="lg:w-1/3 xl:w-1/4 max-w-[300px] mx-auto lg:mx-0">
                  <div className="sticky top-8">
                    <div className="overflow-hidden rounded-3xl border-4 border-white shadow-2xl relative group">
                      <LazyImage
                        src={faculty.profile?.avatarUrl || faculty.image || "/images/faculty/default-avatar.svg"}
                        alt={faculty.name || "Faculty Member"}
                        aspectRatio="aspect-[3/4]"
                        className="h-full w-full object-cover transition-transform duration-700 group-hover:scale-105"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>

                    {/* Quick contact buttons with dynamic hover effects */}
                    <div className="mt-6 w-full space-y-3">
                      <a
                        href={`mailto:${faculty.email}`}
                        className="flex items-center justify-center gap-2 py-3 px-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 text-gray-800 hover:bg-crimson/5 hover:border-crimson/20 group"
                      >
                        <Mail className="h-4 w-4 text-crimson transition-transform duration-300 group-hover:scale-110" />
                        <span className="font-medium">Email</span>
                      </a>
                      
                      {faculty.facultyProfile.websiteUrl && (
                        <a
                          href={faculty.facultyProfile.websiteUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center gap-2 py-3 px-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 text-gray-800 hover:bg-blue-50 hover:border-blue-200 group"
                        >
                          <Globe className="h-4 w-4 text-blue-600 transition-transform duration-300 group-hover:scale-110" />
                          <span className="font-medium">Website</span>
                          <ArrowUpRight className="h-3 w-3 opacity-0 -translate-x-1 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
                        </a>
                      )}
                      
                      {faculty.facultyProfile.scholarId && (
                        <a
                          href={`https://scholar.google.com/citations?user=${faculty.facultyProfile.scholarId}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center gap-2 py-3 px-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300 border border-gray-100 text-gray-800 hover:bg-gold/5 hover:border-gold/20 group"
                        >
                          <Bookmark className="h-4 w-4 text-gold transition-transform duration-300 group-hover:scale-110" />
                          <span className="font-medium">Google Scholar</span>
                          <ArrowUpRight className="h-3 w-3 opacity-0 -translate-x-1 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300" />
                        </a>
                      )}
                    </div>
                    
                    {/* Department Card */}
                    <div className="mt-6 p-5 bg-white rounded-2xl shadow-sm border border-gray-100">
                      <h3 className="text-sm font-medium text-gray-500 mb-2">Department</h3>
                      <Link 
                        href={`/faculty?department=${encodeURIComponent(faculty.facultyProfile.department.name)}`}
                        className="text-base font-medium text-crimson hover:underline inline-flex items-center gap-1"
                      >
                        {faculty.facultyProfile.department.name}
                        <ChevronRight className="h-3 w-3" />
                      </Link>
                      
                      {faculty.facultyProfile.title && (
                        <div className="mt-4 pt-4 border-t border-gray-100">
                          <h3 className="text-sm font-medium text-gray-500 mb-1">Title</h3>
                          <p className="text-base text-gray-900">{faculty.facultyProfile.title}</p>
                        </div>
                      )}
                      
                      {faculty.facultyProfile.officeLocation && (
                        <div className="mt-4 pt-4 border-t border-gray-100">
                          <h3 className="text-sm font-medium text-gray-500 mb-1">Office</h3>
                          <p className="text-base text-gray-900 flex items-start gap-1">
                            <MapPin className="h-4 w-4 text-gray-400 mt-0.5 shrink-0" />
                            <span>{faculty.facultyProfile.officeLocation}</span>
                          </p>
                        </div>
                      )}
                      
                      {faculty.facultyProfile.officeHours && Array.isArray(faculty.facultyProfile.officeHours) && faculty.facultyProfile.officeHours.length > 0 && (
                        <div className="mt-4 pt-4 border-t border-gray-100">
                          <h3 className="text-sm font-medium text-gray-500 mb-1">Office Hours</h3>
                          <div className="text-base text-gray-900">
                            <Clock className="h-4 w-4 text-gray-400 inline mr-1" />
                            <span className="text-sm">
                              {faculty.facultyProfile.officeHours.length} weekly slot{faculty.facultyProfile.officeHours.length !== 1 ? 's' : ''} scheduled
                            </span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="lg:w-2/3 xl:w-3/4 pb-20">
                  <div className="mb-10">
                    <h1 className="text-4xl font-bold text-gray-900 mb-3">{faculty.name}</h1>
                    
                    {faculty.facultyProfile.title && (
                      <p className="text-xl text-gray-600 mb-6">{faculty.facultyProfile.title}</p>
                    )}
                    
                    {faculty.facultyProfile.bio && (
                      <div className="relative mt-8 mb-12">
                        <Quote className="absolute -top-6 -left-2 h-12 w-12 text-crimson/10" />
                        <p className="text-lg text-gray-700 leading-relaxed relative z-10">
                          {faculty.facultyProfile.bio}
                        </p>
                      </div>
                    )}
                    
                    {/* Research Areas Tags */}
                    {faculty.facultyProfile.researchAreas?.length > 0 && (
                      <div className="mt-8">
                        <h3 className="text-base font-semibold text-gray-700 mb-3 flex items-center">
                          <Hash className="h-4 w-4 mr-1 text-crimson" />
                          Research Interests
                        </h3>
                        <div className="flex flex-wrap gap-2">
                          {faculty.facultyProfile.researchAreas.map((area) => (
                            <span 
                              key={area.id} 
                              className="inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium bg-crimson/5 text-crimson border border-crimson/10"
                            >
                              {area.areaName}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Main content tabs */}
                  <Tabs defaultValue="about" className="mt-10">
                    <TabsList className="grid w-full grid-cols-5 mb-8">
                      <TabsTrigger value="about" className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson">
                        About
                      </TabsTrigger>
                      <TabsTrigger value="publications" className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson">
                        Publications
                      </TabsTrigger>
                      <TabsTrigger value="research" className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson">
                        Research
                      </TabsTrigger>
                      <TabsTrigger value="teaching" className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson">
                        Teaching
                      </TabsTrigger>
                      <TabsTrigger value="experience" className="data-[state=active]:bg-crimson/10 data-[state=active]:text-crimson">
                        Experience
                      </TabsTrigger>
                    </TabsList>

                    {/* About Tab */}
                    <TabsContent value="about" className="space-y-8">
                      {/* Education Timeline */}
                      {faculty.facultyProfile.education?.length > 0 && (
                        <section className="bg-white rounded-3xl p-6 shadow-sm border border-gray-100">
                          <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-800">
                            <GraduationCap className="mr-2 h-5 w-5 text-crimson" />
                            Education
                          </h2>
                          <EducationTimeline education={faculty.facultyProfile.education} />
                        </section>
                      )}

                      {/* Skills */}
                      {faculty.facultyProfile.skills?.length > 0 && (
                        <section className="bg-white rounded-3xl p-6 shadow-sm border border-gray-100">
                          <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-800">
                            <Award className="mr-2 h-5 w-5 text-crimson" />
                            Skills & Expertise
                          </h2>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {Array.from(new Set(faculty.facultyProfile.skills.map(skill => skill.category))).map(category => (
                              <div key={category} className="space-y-4">
                                <h3 className="text-lg font-medium text-gray-700">{category}</h3>
                                <div className="space-y-3">
                                  {faculty.facultyProfile.skills
                                    .filter(skill => skill.category === category)
                                    .map(skill => (
                                      <div key={skill.id} className="space-y-1">
                                        <div className="flex justify-between items-center">
                                          <span className="text-gray-800">{skill.skillName}</span>
                                          <span className="text-sm text-gray-500">{skill.proficiency}%</span>
                                        </div>
                                        <div className="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
                                          <div 
                                            className="h-full bg-gradient-to-r from-crimson to-gold rounded-full"
                                            style={{ width: `${skill.proficiency}%` }}
                                          ></div>
                                        </div>
                                      </div>
                                    ))
                                  }
                                </div>
                              </div>
                            ))}
                          </div>
                        </section>
                      )}

                      {/* Academic Timeline */}
                      {faculty.facultyProfile.timeline?.length > 0 && (
                        <section className="bg-white rounded-3xl p-6 shadow-sm border border-gray-100">
                          <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-800">
                            <Calendar className="mr-2 h-5 w-5 text-crimson" />
                            Academic Timeline
                          </h2>
                          <AcademicTimeline events={faculty.facultyProfile.timeline} />
                        </section>
                      )}
                    </TabsContent>

                    {/* Publications Tab */}
                    <TabsContent value="publications" className="space-y-8">
                      <section className="bg-white rounded-3xl p-6 shadow-sm border border-gray-100">
                        <ScholarlyPublications 
                          initialPublications={faculty.facultyProfile.publications} 
                          scholarId={faculty.facultyProfile.scholarId || undefined}
                        />
                      </section>
                    </TabsContent>

                    {/* Research Tab */}
                    <TabsContent value="research" className="space-y-8">
                      {/* Research Areas */}
                      {faculty.facultyProfile.researchAreas?.length > 0 && (
                        <section className="bg-white rounded-3xl p-6 shadow-sm border border-gray-100">
                          <ResearchAreas areas={faculty.facultyProfile.researchAreas} />
                        </section>
                      )}

                      {/* Research Projects */}
                      {faculty.facultyProfile.researchProjects?.length > 0 && (
                        <section className="bg-white rounded-3xl p-6 shadow-sm border border-gray-100">
                          <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-800">
                            <FileText className="mr-2 h-5 w-5 text-crimson" />
                            Research Projects
                          </h2>
                          <ResearchOpportunities projects={faculty.facultyProfile.researchProjects} />
                        </section>
                      )}
                    </TabsContent>

                    {/* Teaching Tab */}
                    <TabsContent value="teaching" className="space-y-8">
                      {/* Current Classes */}
                      <section className="bg-white rounded-3xl p-6 shadow-sm border border-gray-100">
                        <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-800">
                          <BookOpen className="mr-2 h-5 w-5 text-crimson" />
                          Current Classes
                        </h2>
                        <UpcomingClasses classes={faculty.facultyProfile.classes} />
                      </section>

                      {/* Office Hours */}
                      {faculty.facultyProfile.officeHours && faculty.facultyProfile.officeHours.length > 0 && (
                        <section className="bg-white rounded-3xl p-6 shadow-sm border border-gray-100">
                          <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-800">
                            <Clock className="mr-2 h-5 w-5 text-crimson" />
                            Office Hours
                          </h2>
                          <div className="space-y-4">
                            {faculty.facultyProfile.officeHours.map((hour: any) => {
                              const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
                              const formatTime = (time: string) => {
                                const [h, m] = time.split(':')
                                const hourNum = parseInt(h)
                                const ampm = hourNum >= 12 ? 'PM' : 'AM'
                                const hour12 = hourNum % 12 || 12
                                return `${hour12}:${m} ${ampm}`
                              }

                              return (
                                <div key={hour.id} className="p-4 border border-gray-200 rounded-xl bg-gray-50">
                                  <div className="flex items-center justify-between">
                                    <div className="flex items-center space-x-3">
                                      <Calendar className="w-5 h-5 text-crimson" />
                                      <span className="font-medium text-gray-900">{days[hour.dayOfWeek]}</span>
                                      <span className="text-gray-600">
                                        {formatTime(hour.startTime)} - {formatTime(hour.endTime)}
                                      </span>
                                    </div>
                                    <div className="flex items-center text-gray-600">
                                      <MapPin className="w-4 h-4 mr-1" />
                                      {hour.location}
                                    </div>
                                  </div>
                                  {hour.notes && (
                                    <p className="text-sm text-gray-600 mt-2">{hour.notes}</p>
                                  )}
                                </div>
                              )
                            })}
                          </div>
                        </section>
                      )}
                    </TabsContent>

                    {/* Experience Tab */}
                    <TabsContent value="experience" className="space-y-8">
                      {/* Industry Experience */}
                      {faculty.facultyProfile.industryExperience?.length > 0 && (
                        <section className="bg-white rounded-3xl p-6 shadow-sm border border-gray-100">
                          <h2 className="text-xl font-semibold mb-6 flex items-center text-gray-800">
                            <Briefcase className="mr-2 h-5 w-5 text-crimson" />
                            Industry Experience
                          </h2>
                          <div className="space-y-8">
                            {faculty.facultyProfile.industryExperience.map(exp => {
                              const startDate = exp.startDate ? new Date(exp.startDate).getFullYear() : '';
                              const endDate = exp.endDate ? new Date(exp.endDate).getFullYear() : 'Present';
                              
                              return (
                                <div key={exp.id} className="relative pl-10 border-l-2 border-gray-200 pb-8">
                                  <div className="absolute -left-2 top-0 h-4 w-4 rounded-full bg-crimson"></div>
                                  <div className="mb-1 text-xl font-medium text-gray-900">{exp.position}</div>
                                  <div className="text-gray-700 mb-1">{exp.company}</div>
                                  <div className="text-sm text-gray-500 mb-3">{startDate} — {endDate}</div>
                                  {exp.description && (
                                    <p className="text-gray-600">{exp.description}</p>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        </section>
                      )}
                    </TabsContent>
                  </Tabs>
                </div>
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </PageTransition>
  )
}

export async function generateStaticParams() {
  const users = await prisma.user.findMany({
    where: {
      role: 'FACULTY',
      status: 'ACTIVE',
    },
    select: {
      id: true,
    },
  });

  return users.map((user) => ({
    id: user.id,
  }));
}