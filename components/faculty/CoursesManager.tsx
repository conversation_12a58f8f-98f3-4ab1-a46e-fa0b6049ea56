"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { BookOpen, Edit, Users, Calendar, MapPin, FileText, ExternalLink, Loader2 } from "lucide-react"

interface CourseClass {
  id: string
  semester: string
  year: number
  section?: string | null
  enrollmentCount: number
  maxEnrollment: number
  schedule: any // JSON object
  syllabusUrl?: string | null
  description?: string | null
  status: string
  course: {
    id: string
    code: string
    name: string
    description?: string | null
    credits: number
    department: {
      name: string
      slug: string
    }
  }
}

interface CoursesManagerProps {
  initialCourses: CourseClass[]
}

export function CoursesManager({ initialCourses }: CoursesManagerProps) {
  const [courses, setCourses] = useState<CourseClass[]>(initialCourses)
  const [isLoading, setIsLoading] = useState(false)
  const [editingId, setEditingId] = useState<string | null>(null)
  const [showEditForm, setShowEditForm] = useState(false)
  const [formData, setFormData] = useState({
    syllabusUrl: "",
    description: "",
    maxEnrollment: 0
  })

  const { toast } = useToast()
  const router = useRouter()

  const resetForm = () => {
    setFormData({
      syllabusUrl: "",
      description: "",
      maxEnrollment: 0
    })
    setEditingId(null)
    setShowEditForm(false)
  }

  const handleEdit = (courseClass: CourseClass) => {
    setFormData({
      syllabusUrl: courseClass.syllabusUrl || "",
      description: courseClass.description || "",
      maxEnrollment: courseClass.maxEnrollment
    })
    setEditingId(courseClass.id)
    setShowEditForm(true)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const submitData = {
        courseClassId: editingId,
        syllabusUrl: formData.syllabusUrl || undefined,
        description: formData.description || undefined,
        maxEnrollment: formData.maxEnrollment
      }

      const response = await fetch('/api/faculty/courses', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update course')
      }

      const result = await response.json()

      toast({
        title: "Course Updated",
        description: "Your course details have been updated successfully.",
      })

      // Refresh the page to show updated data
      router.refresh()
      resetForm()
    } catch (error) {
      console.error('Error updating course:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update course. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'current': return 'bg-green-100 text-green-800'
      case 'upcoming': return 'bg-blue-100 text-blue-800'
      case 'past': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const formatSchedule = (schedule: any) => {
    if (!schedule || typeof schedule !== 'object') return 'Schedule TBD'
    
    try {
      const { days, time, location } = schedule
      const daysStr = Array.isArray(days) ? days.join(', ') : 'TBD'
      const timeStr = time || 'TBD'
      const locationStr = location || 'TBD'
      
      return `${daysStr} • ${timeStr} • ${locationStr}`
    } catch {
      return 'Schedule TBD'
    }
  }

  // Group courses by status
  const groupedCourses = courses.reduce((acc, course) => {
    const status = course.status || 'upcoming'
    if (!acc[status]) {
      acc[status] = []
    }
    acc[status].push(course)
    return acc
  }, {} as Record<string, CourseClass[]>)

  const statusOrder = ['current', 'upcoming', 'past']
  const sortedStatuses = statusOrder.filter(status => groupedCourses[status]?.length > 0)

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">My Courses</h1>
        <p className="text-gray-600">Manage your teaching assignments and course information</p>
      </div>

      {/* Courses by Status */}
      <div className="space-y-6">
        {sortedStatuses.length > 0 ? (
          sortedStatuses.map((status) => (
            <Card key={status}>
              <CardHeader>
                <CardTitle className="flex items-center capitalize">
                  <BookOpen className="w-5 h-5 mr-2" />
                  {status} Courses
                  <Badge variant="secondary" className="ml-2">
                    {groupedCourses[status].length}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {groupedCourses[status].map((courseClass) => (
                    <div key={courseClass.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="font-semibold text-lg">
                              {courseClass.course.code}: {courseClass.course.name}
                            </h3>
                            <Badge className={getStatusColor(courseClass.status)}>
                              {courseClass.status}
                            </Badge>
                            {courseClass.section && (
                              <Badge variant="outline">
                                Section {courseClass.section}
                              </Badge>
                            )}
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                            <div className="flex items-center">
                              <Calendar className="w-4 h-4 mr-1" />
                              {courseClass.semester} {courseClass.year}
                            </div>
                            <div className="flex items-center">
                              <Users className="w-4 h-4 mr-1" />
                              {courseClass.enrollmentCount}/{courseClass.maxEnrollment} students
                            </div>
                            <div className="flex items-center">
                              <MapPin className="w-4 h-4 mr-1" />
                              {courseClass.course.department.name}
                            </div>
                            <div className="flex items-center">
                              <BookOpen className="w-4 h-4 mr-1" />
                              {courseClass.course.credits} credits
                            </div>
                          </div>

                          <div className="text-sm text-gray-600 mb-2">
                            <strong>Schedule:</strong> {formatSchedule(courseClass.schedule)}
                          </div>

                          {courseClass.course.description && (
                            <p className="text-sm text-gray-700 mb-2">
                              <strong>Course Description:</strong> {courseClass.course.description}
                            </p>
                          )}

                          {courseClass.description && (
                            <p className="text-sm text-gray-700 mb-2">
                              <strong>Section Notes:</strong> {courseClass.description}
                            </p>
                          )}

                          {courseClass.syllabusUrl && (
                            <div className="flex items-center mt-2">
                              <FileText className="w-4 h-4 mr-1 text-blue-600" />
                              <a 
                                href={courseClass.syllabusUrl} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
                              >
                                View Syllabus
                                <ExternalLink className="w-3 h-3 ml-1" />
                              </a>
                            </div>
                          )}
                        </div>
                        
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(courseClass)}
                            disabled={isLoading}
                          >
                            <Edit className="w-4 h-4 mr-1" />
                            Edit
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card>
            <CardContent className="text-center py-8">
              <BookOpen className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No courses assigned</h3>
              <p className="text-gray-500">You don't have any course assignments yet. Contact your department administrator to get courses assigned.</p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Edit Course Form */}
      {showEditForm && (
        <Card>
          <CardHeader>
            <CardTitle>Edit Course Details</CardTitle>
            <CardDescription>
              Update course information, syllabus, and enrollment settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label htmlFor="syllabusUrl">Syllabus URL</Label>
                <Input
                  id="syllabusUrl"
                  type="url"
                  value={formData.syllabusUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, syllabusUrl: e.target.value }))}
                  placeholder="https://example.com/syllabus.pdf"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Link to your course syllabus (PDF, Google Drive, etc.)
                </p>
              </div>

              <div>
                <Label htmlFor="maxEnrollment">Maximum Enrollment</Label>
                <Input
                  id="maxEnrollment"
                  type="number"
                  min="1"
                  value={formData.maxEnrollment}
                  onChange={(e) => setFormData(prev => ({ ...prev, maxEnrollment: parseInt(e.target.value) || 0 }))}
                  placeholder="30"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Maximum number of students that can enroll in this course
                </p>
              </div>

              <div>
                <Label htmlFor="description">Section Notes</Label>
                <Textarea
                  id="description"
                  rows={4}
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Add any specific notes or information about this course section..."
                />
                <p className="text-xs text-gray-500 mt-1">
                  Additional information specific to your section of this course
                </p>
              </div>

              <div className="flex justify-end space-x-4">
                <Button type="button" variant="outline" onClick={resetForm} disabled={isLoading}>
                  Cancel
                </Button>
                <Button type="submit" disabled={isLoading}>
                  {isLoading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
                  Update Course
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
