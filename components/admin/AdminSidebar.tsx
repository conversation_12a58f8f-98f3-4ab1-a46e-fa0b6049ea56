"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState } from "react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, <PERSON>ltipContent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  Users,
  FileText,
  Building2,
  BookOpen,
  Calendar,
  Settings,
  BarChart3,
  Home,
  UserCheck,
  GraduationCap,
  Database,
  ChevronLeft,
  ChevronRight,
  Bell,
  Activity,
  Shield,
  Zap,
  TrendingUp,
  Globe
} from "lucide-react"
import { UserRole } from "@prisma/client"

interface AdminSidebarProps {
  user: {
    id: string
    email: string
    name?: string | null
    role: UserRole
  }
}

const navigationSections = [
  {
    title: "Overview",
    items: [
      {
        name: "Dashboard",
        href: "/admin",
        icon: Home,
        description: "Main dashboard overview",
        badge: null
      }
    ]
  },
  {
    title: "Management",
    items: [
      {
        name: "Faculty",
        href: "/admin/faculty",
        icon: GraduationCap,
        description: "Manage faculty profiles",
        badge: null
      },
      {
        name: "Users",
        href: "/admin/users",
        icon: Users,
        description: "User account management",
        badge: null
      },
      {
        name: "Content",
        href: "/admin/posts",
        icon: FileText,
        description: "Posts and articles",
        badge: null
      }
    ]
  },
  {
    title: "Academic",
    items: [
      {
        name: "Departments",
        href: "/admin/departments",
        icon: Building2,
        description: "Academic departments",
        badge: null
      },
      {
        name: "Programs",
        href: "/admin/programs",
        icon: BookOpen,
        description: "Degree programs",
        badge: null
      },
      {
        name: "Courses",
        href: "/admin/courses",
        icon: Calendar,
        description: "Course catalog",
        badge: null
      }
    ]
  },
  {
    title: "Analytics & Tools",
    items: [
      {
        name: "Analytics",
        href: "/admin/analytics",
        icon: BarChart3,
        description: "Performance insights",
        badge: { text: "New", variant: "default" as const }
      },
      {
        name: "Database",
        href: "/admin/database",
        icon: Database,
        description: "Database explorer",
        badge: { text: "Dev", variant: "secondary" as const }
      }
    ]
  },
  {
    title: "System",
    items: [
      {
        name: "Settings",
        href: "/admin/settings",
        icon: Settings,
        description: "System configuration",
        badge: null
      }
    ]
  }
]

export function AdminSidebar({ user }: AdminSidebarProps) {
  const pathname = usePathname()
  const [isCollapsed, setIsCollapsed] = useState(false)

  const getRoleIcon = (role: UserRole) => {
    switch (role) {
      case 'SYS_ADMIN':
        return Shield
      case 'COLLEGE_ADMIN':
        return UserCheck
      default:
        return UserCheck
    }
  }

  const RoleIcon = getRoleIcon(user.role)

  return (
    <TooltipProvider>
      <div className={cn(
        "flex flex-col bg-gradient-to-b from-slate-900 via-slate-800 to-slate-900 shadow-2xl transition-all duration-300 ease-in-out",
        isCollapsed ? "w-16" : "w-72"
      )}>
        {/* Header */}
        <div className="relative">
          <div className={cn(
            "flex items-center px-6 py-4 border-b border-slate-700/50",
            isCollapsed && "justify-center px-4"
          )}>
            {!isCollapsed ? (
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <Shield className="w-4 h-4 text-white" />
                </div>
                <div>
                  <h1 className="text-lg font-bold text-white">Admin Panel</h1>
                  <p className="text-xs text-slate-400">College Management</p>
                </div>
              </div>
            ) : (
              <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                <Shield className="w-4 h-4 text-white" />
              </div>
            )}
          </div>

          {/* Collapse Toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className={cn(
              "absolute -right-3 top-6 w-6 h-6 rounded-full bg-slate-800 border border-slate-600 text-slate-300 hover:text-white hover:bg-slate-700 transition-colors",
              "flex items-center justify-center p-0"
            )}
          >
            {isCollapsed ? (
              <ChevronRight className="w-3 h-3" />
            ) : (
              <ChevronLeft className="w-3 h-3" />
            )}
          </Button>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-3 py-6 space-y-6 overflow-y-auto">
          {navigationSections.map((section) => (
            <div key={section.title}>
              {!isCollapsed && (
                <h3 className="px-3 mb-3 text-xs font-semibold text-slate-400 uppercase tracking-wider">
                  {section.title}
                </h3>
              )}
              <div className="space-y-1">
                {section.items.map((item) => {
                  const isActive = pathname === item.href ||
                    (item.href !== "/admin" && pathname.startsWith(item.href))

                  const NavItem = (
                    <Link
                      key={item.name}
                      href={item.href}
                      className={cn(
                        "group flex items-center px-3 py-2.5 text-sm font-medium rounded-xl transition-all duration-200",
                        isActive
                          ? "bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg shadow-blue-500/25"
                          : "text-slate-300 hover:text-white hover:bg-slate-700/50",
                        isCollapsed && "justify-center"
                      )}
                    >
                      <item.icon className={cn(
                        "flex-shrink-0 transition-colors",
                        isActive ? "text-white" : "text-slate-400 group-hover:text-white",
                        isCollapsed ? "w-5 h-5" : "w-5 h-5 mr-3"
                      )} />

                      {!isCollapsed && (
                        <>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <span className="truncate">{item.name}</span>
                              {item.badge && (
                                <Badge
                                  variant={item.badge.variant}
                                  className="ml-2 text-xs"
                                >
                                  {item.badge.text}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </>
                      )}
                    </Link>
                  )

                  if (isCollapsed) {
                    return (
                      <Tooltip key={item.name}>
                        <TooltipTrigger asChild>
                          {NavItem}
                        </TooltipTrigger>
                        <TooltipContent side="right" className="flex items-center space-x-2">
                          <span>{item.name}</span>
                          {item.badge && (
                            <Badge variant={item.badge.variant} className="text-xs">
                              {item.badge.text}
                            </Badge>
                          )}
                        </TooltipContent>
                      </Tooltip>
                    )
                  }

                  return NavItem
                })}
              </div>
            </div>
          ))}
        </nav>

        {/* User Profile */}
        <div className="border-t border-slate-700/50 p-4">
          <div className={cn(
            "flex items-center space-x-3",
            isCollapsed && "justify-center"
          )}>
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-blue-600 rounded-full flex items-center justify-center">
                <RoleIcon className="w-5 h-5 text-white" />
              </div>
              <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-slate-800 flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            </div>

            {!isCollapsed && (
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">
                  {user.name || user.email}
                </p>
                <p className="text-xs text-slate-400 capitalize truncate">
                  {user.role.toLowerCase().replace('_', ' ')}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}
