'use client'

import React from 'react'
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar, Clock, MapPin, Users, ExternalLink } from "lucide-react"

export interface CourseClass {
  id: string
  courseCode: string
  courseName: string
  semester: string
  schedule: {
    days: string[]
    time: string
    location: string
  }
  enrollmentCount: number
  maxEnrollment: number
  syllabus?: string
  description?: string
  status: 'upcoming' | 'current' | 'past'
}

interface UpcomingClassesProps {
  classes: CourseClass[]
}

export function UpcomingClasses({ classes }: UpcomingClassesProps) {
  // Group classes by status
  const currentClasses = classes.filter(c => c.status === 'current')
  const upcomingClasses = classes.filter(c => c.status === 'upcoming')
  const recentClasses = classes.filter(c => c.status === 'past')
  
  // Format days as "Monday, Wednesday, Friday"
  const formatDays = (days: string[]) => {
    const fullDays = days.map(d => {
      switch (d) {
        case 'M': return 'Monday';
        case 'T': return 'Tuesday';
        case 'W': return 'Wednesday';
        case 'Th': return 'Thursday';
        case 'F': return 'Friday';
        case 'Sa': return 'Saturday';
        case 'Su': return 'Sunday';
        default: return d;
      }
    });
    
    return fullDays.join(', ');
  };
  
  // Format enrollment as "25/30"
  const formatEnrollment = (current: number, max: number) => {
    return `${current}/${max}`;
  };
  
  // Get enrollment status and styling
  // Use specific badge variant types from shadcn/ui
  type BadgeVariant = 'outline' | 'default' | 'destructive' | 'secondary';
  
  const getEnrollmentStatus = (current: number, max: number): { label: string, variant: BadgeVariant } => {
    const ratio = current / max;
    if (ratio >= 0.9) return { label: "Near Capacity", variant: "destructive" };
    if (ratio >= 0.7) return { label: "Filling Up", variant: "secondary" };
    return { label: "Open", variant: "outline" };
  };
  
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-semibold mb-4">Teaching Schedule</h2>
      
      {currentClasses.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Current Semester</h3>
          {currentClasses.map((course) => (
            <Card key={course.id} className="overflow-hidden">
              <CardContent className="p-5">
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold">{course.courseCode}</h4>
                      <Badge 
                        variant={getEnrollmentStatus(course.enrollmentCount, course.maxEnrollment).variant}
                      >
                        {getEnrollmentStatus(course.enrollmentCount, course.maxEnrollment).label}
                      </Badge>
                    </div>
                    <h3 className="text-lg font-medium mb-3">{course.courseName}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-y-2 gap-x-4 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3.5 w-3.5" />
                        {formatDays(course.schedule.days)}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-3.5 w-3.5" />
                        {course.schedule.time}
                      </span>
                      <span className="flex items-center gap-1">
                        <MapPin className="h-3.5 w-3.5" />
                        {course.schedule.location}
                      </span>
                      <span className="flex items-center gap-1">
                        <Users className="h-3.5 w-3.5" />
                        Enrollment: {formatEnrollment(course.enrollmentCount, course.maxEnrollment)}
                      </span>
                    </div>
                  </div>
                  
                  {course.syllabus && (
                    <div className="flex-shrink-0">
                      <a href={course.syllabus} target="_blank" rel="noopener noreferrer">
                        <Button variant="outline" size="sm">
                          <ExternalLink className="mr-1 h-3.5 w-3.5" />
                          Syllabus
                        </Button>
                      </a>
                    </div>
                  )}
                </div>
                
                {course.description && (
                  <p className="text-sm text-muted-foreground mt-4">
                    {course.description}
                  </p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
      
      {upcomingClasses.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Upcoming Semester</h3>
          {upcomingClasses.map((course) => (
            <Card key={course.id} className="overflow-hidden">
              <CardContent className="p-5">
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold">{course.courseCode}</h4>
                      <Badge variant="outline">{course.semester}</Badge>
                    </div>
                    <h3 className="text-lg font-medium mb-3">{course.courseName}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-y-2 gap-x-4 text-sm text-muted-foreground">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3.5 w-3.5" />
                        {formatDays(course.schedule.days)}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-3.5 w-3.5" />
                        {course.schedule.time}
                      </span>
                      <span className="flex items-center gap-1">
                        <MapPin className="h-3.5 w-3.5" />
                        {course.schedule.location}
                      </span>
                    </div>
                  </div>
                  
                  {course.syllabus && (
                    <div className="flex-shrink-0">
                      <a href={course.syllabus} target="_blank" rel="noopener noreferrer">
                        <Button variant="outline" size="sm">
                          <ExternalLink className="mr-1 h-3.5 w-3.5" />
                          Syllabus
                        </Button>
                      </a>
                    </div>
                  )}
                </div>
                
                {course.description && (
                  <p className="text-sm text-muted-foreground mt-4">
                    {course.description}
                  </p>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
      
      {recentClasses.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Recent Classes</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {recentClasses.slice(0, 4).map((course) => (
              <Card key={course.id} className="overflow-hidden bg-muted/20">
                <CardContent className="p-4">
                  <div>
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold">{course.courseCode}</h4>
                      <Badge variant="outline">{course.semester}</Badge>
                    </div>
                    <h3 className="text-md font-medium">{course.courseName}</h3>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
      
      {classes.length === 0 && (
        <div className="text-center py-12 border rounded-lg bg-muted/20">
          <p className="text-muted-foreground">No class information available.</p>
        </div>
      )}
    </div>
  )
} 